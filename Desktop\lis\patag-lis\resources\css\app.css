/* Import Bootstrap */
@import 'bootstrap/dist/css/bootstrap.min.css';

/* Custom CSS Variables for Professional Color Scheme */
:root {
    --primary-color: #2c5aa0;      /* Professional Blue */
    --secondary-color: #28a745;    /* Success Green */
    --accent-color: #ffc107;       /* Warning Yellow */
    --dark-color: #343a40;         /* Dark Gray */
    --light-color: #f8f9fa;        /* Light Gray */
    --white: #ffffff;
    --text-dark: #212529;
    --text-muted: #6c757d;
}

/* Override Bootstrap primary colors */
.btn-primary {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

.btn-primary:hover {
    background-color: #1e3d6f;
    border-color: #1e3d6f;
}

.bg-primary {
    background-color: var(--primary-color) !important;
}

.text-primary {
    color: var(--primary-color) !important;
}

/* Custom styles for the LIS */
.navbar-brand {
    font-weight: 600;
    font-size: 1.25rem;
}

.card {
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    border: 1px solid rgba(0, 0, 0, 0.125);
}

.sidebar {
    background-color: var(--primary-color);
    min-height: 100vh;
}

.sidebar .nav-link {
    color: rgba(255, 255, 255, 0.8);
    padding: 0.75rem 1rem;
    border-radius: 0.375rem;
    margin-bottom: 0.25rem;
}

.sidebar .nav-link:hover,
.sidebar .nav-link.active {
    color: var(--white);
    background-color: rgba(255, 255, 255, 0.1);
}

.main-content {
    background-color: var(--light-color);
    min-height: 100vh;
}

/* Form styling */
.form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(44, 90, 160, 0.25);
}

/* Table styling */
.table th {
    background-color: var(--primary-color);
    color: var(--white);
    border: none;
}

/* Login page styling */
.login-container {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    min-height: 100vh;
}

.login-card {
    background: var(--white);
    border-radius: 1rem;
    box-shadow: 0 1rem 3rem rgba(0, 0, 0, 0.175);
}
