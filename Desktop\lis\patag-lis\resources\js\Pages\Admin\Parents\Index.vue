<template>
  <AdminLayout page-title="Parents/Guardian Management">
    <!-- Header Section -->
    <div class="row mb-4">
      <div class="col-md-8">
        <h2 class="fw-bold text-primary mb-2">
          <i class="fas fa-users me-3"></i>
          Parents/Guardian Management
        </h2>
        <p class="text-muted mb-0">Manage parent and guardian accounts, contact information, and student relationships</p>
      </div>
        <div class="col-md-4 text-end">
          <Link
            href="/admin/parents/create"
            class="btn btn-primary btn-sm px-3 shadow-sm"
            aria-label="Add new parent/guardian"
          >
            <i class="fas fa-plus me-1"></i>
            Add Parent/Guardian
          </Link>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
      <div class="col-md-3">
        <div class="card border-0 shadow-sm bg-gradient-primary text-white">
          <div class="card-body">
            <div class="d-flex align-items-center">
              <div class="flex-grow-1">
                <h6 class="card-title mb-1">Total Parents</h6>
                <h3 class="mb-0">{{ parents.total || parents.data.length }}</h3>
              </div>
              <div class="ms-3">
                <i class="fas fa-users fa-2x opacity-75"></i>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="col-md-3">
        <div class="card border-0 shadow-sm bg-gradient-success text-white">
          <div class="card-body">
            <div class="d-flex align-items-center">
              <div class="flex-grow-1">
                <h6 class="card-title mb-1">Active Parents</h6>
                <h3 class="mb-0">{{ parents.data.length }}</h3>
              </div>
              <div class="ms-3">
                <i class="fas fa-user-check fa-2x opacity-75"></i>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="col-md-3">
        <div class="card border-0 shadow-sm bg-gradient-info text-white">
          <div class="card-body">
            <div class="d-flex align-items-center">
              <div class="flex-grow-1">
                <h6 class="card-title mb-1">With Students</h6>
                <h3 class="mb-0">{{ parentsWithStudents }}</h3>
              </div>
              <div class="ms-3">
                <i class="fas fa-child fa-2x opacity-75"></i>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="col-md-3">
        <div class="card border-0 shadow-sm bg-gradient-warning text-white">
          <div class="card-body">
            <div class="d-flex align-items-center">
              <div class="flex-grow-1">
                <h6 class="card-title mb-1">New This Month</h6>
                <h3 class="mb-0">{{ newThisMonth }}</h3>
              </div>
              <div class="ms-3">
                <i class="fas fa-user-plus fa-2x opacity-75"></i>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Success Message -->
    <div v-if="$page.props.flash.success" class="alert alert-success alert-dismissible fade show" role="alert">
      <i class="fas fa-check-circle me-2"></i>
      {{ $page.props.flash.success }}
      <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>

    <!-- Search and Filter Section -->
    <div class="card border-0 shadow-sm mb-4">
      <div class="card-body">
        <div class="row align-items-center">
          <div class="col-md-6">
            <div class="input-group">
              <span class="input-group-text bg-light border-end-0">
                <i class="fas fa-search text-muted"></i>
              </span>
              <input
                v-model="searchQuery"
                type="text"
                class="form-control border-start-0"
                placeholder="Search parents by name or email..."
                @input="filterParents"
              >
            </div>
          </div>
          <div class="col-md-6 text-end">
            <div class="btn-group" role="group">
              <button
                type="button"
                class="btn btn-outline-secondary"
                :class="{ 'active': filterStatus === 'all' }"
                @click="setFilter('all')"
              >
                All Parents
              </button>
              <button
                type="button"
                class="btn btn-outline-secondary"
                :class="{ 'active': filterStatus === 'with_students' }"
                @click="setFilter('with_students')"
              >
                With Students
              </button>
              <button
                type="button"
                class="btn btn-outline-secondary"
                :class="{ 'active': filterStatus === 'without_students' }"
                @click="setFilter('without_students')"
              >
                Without Students
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Parents Table -->
    <div class="card border-0 shadow-sm">
      <div class="card-header bg-gradient-primary text-white">
        <div class="d-flex align-items-center justify-content-between">
          <h6 class="m-0 fw-bold">
            <i class="fas fa-list me-2"></i>
            Parents/Guardian Directory
          </h6>
          <span class="badge bg-white text-primary">{{ filteredParents.length }} parents</span>
        </div>
      </div>
      <div class="card-body p-0">
        <div class="table-responsive">
          <table class="table table-hover mb-0 parents-table">
            <thead class="table-light">
              <tr>
                <th class="border-0 ps-4 fw-bold">Parent Information</th>
                <th class="border-0 fw-bold">Contact</th>
                <th class="border-0 fw-bold">Students</th>
                <th class="border-0 fw-bold">Status</th>
                <th class="border-0 text-center fw-bold">Actions</th>
              </tr>
            </thead>
            <tbody>
              <tr v-for="parent in filteredParents" :key="parent.id" class="parent-row">
                <td class="ps-4 py-3">
                  <div class="d-flex align-items-center">
                    <div class="parent-avatar bg-gradient-primary text-white rounded-circle d-flex align-items-center justify-content-center me-3">
                      <i class="fas fa-user"></i>
                    </div>
                    <div>
                      <div class="fw-bold text-dark">{{ parent.name }}</div>
                      <small class="text-muted">
                        <i class="fas fa-id-badge me-1"></i>
                        Parent ID: {{ parent.id }}
                      </small>
                    </div>
                  </div>
                </td>
                <td class="py-3">
                  <div>
                    <div class="text-dark mb-1">
                      <i class="fas fa-envelope me-2 text-muted"></i>
                      {{ parent.email }}
                    </div>
                    <small class="text-muted">
                      <i class="fas fa-calendar me-1"></i>
                      Joined {{ formatDate(parent.created_at) }}
                    </small>
                  </div>
                </td>
                <td class="py-3">
                  <div>
                    <span class="badge bg-info-soft text-info mb-1">
                      <i class="fas fa-child me-1"></i>
                      {{ parent.students_count || 0 }} students
                    </span>
                    <div v-if="parent.students_count > 0">
                      <small class="text-muted">Guardian</small>
                    </div>
                    <div v-else>
                      <small class="text-warning">No students assigned</small>
                    </div>
                  </div>
                </td>
                <td class="py-3">
                  <span class="badge bg-success-soft text-success">
                    <i class="fas fa-check-circle me-1"></i>
                    Active
                  </span>
                </td>
                <td class="text-center py-3">
                  <div class="btn-group" role="group">
                    <button
                      @click="viewParent(parent)"
                      class="btn btn-sm btn-outline-primary"
                      title="View Details"
                    >
                      <i class="fas fa-eye"></i>
                    </button>
                    <button
                      @click="editParent(parent)"
                      class="btn btn-sm btn-outline-warning"
                      title="Edit Parent"
                    >
                      <i class="fas fa-edit"></i>
                    </button>
                    <button
                      @click="confirmDelete(parent)"
                      class="btn btn-sm btn-outline-danger"
                      title="Delete Parent"
                    >
                      <i class="fas fa-trash"></i>
                    </button>
                  </div>
                </td>
              </tr>
              <tr v-if="filteredParents.length === 0">
                <td colspan="5" class="text-center py-5">
                  <div class="empty-state">
                    <i class="fas fa-users fa-4x text-muted mb-3"></i>
                    <h5 class="text-muted">No parents found</h5>
                    <p class="text-muted mb-3">
                      {{ searchQuery ? 'Try adjusting your search criteria' : 'Get started by adding your first parent/guardian' }}
                    </p>
                    <Link v-if="!searchQuery" href="/admin/parents/create" class="btn btn-primary">
                      <i class="fas fa-plus me-2"></i>
                      Add First Parent/Guardian
                    </Link>
                  </div>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
      
      <!-- Pagination -->
      <div v-if="parents.links && parents.links.length > 3" class="card-footer bg-white border-top">
        <nav>
          <ul class="pagination justify-content-center mb-0">
            <li v-for="link in parents.links" :key="link.label" class="page-item" :class="{ active: link.active, disabled: !link.url }">
              <Link v-if="link.url" :href="link.url" class="page-link" v-html="link.label"></Link>
              <span v-else class="page-link" v-html="link.label"></span>
            </li>
          </ul>
        </nav>
      </div>
    </div>

    <!-- View Parent Modal -->
    <div class="modal fade" id="viewParentModal" tabindex="-1" aria-labelledby="viewParentModalLabel" aria-hidden="true">
      <div class="modal-dialog modal-xl">
        <div class="modal-content">
          <div class="modal-header bg-gradient-info text-white">
            <h5 class="modal-title" id="viewParentModalLabel">
              <i class="fas fa-user-circle me-2"></i>
              Parent/Guardian Profile
            </h5>
            <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
          </div>
          <div class="modal-body p-0" v-if="parentToView">
            <div class="row g-0">
              <!-- Parent Information Card -->
              <div class="col-lg-4 bg-light border-end">
                <div class="p-4">
                  <div class="text-center mb-4">
                    <div class="parent-avatar-large bg-gradient-primary text-white rounded-circle mx-auto mb-3 d-flex align-items-center justify-content-center">
                      <i class="fas fa-user fa-2x"></i>
                    </div>
                    <h5 class="mb-1">{{ parentToView.name }}</h5>
                    <p class="text-muted mb-0">{{ parentToView.email }}</p>
                  </div>

                  <div class="info-section mb-4">
                    <h6 class="text-uppercase text-muted fw-bold mb-3" style="font-size: 0.75rem; letter-spacing: 1px;">
                      <i class="fas fa-info-circle me-2"></i>Basic Information
                    </h6>

                    <div class="info-item mb-3">
                      <label class="fw-bold text-muted small">ROLE</label>
                      <div class="mt-1">
                        <span class="badge bg-primary-soft text-primary">
                          <i class="fas fa-users me-1"></i>
                          Parent/Guardian
                        </span>
                      </div>
                    </div>

                    <div class="info-item mb-3">
                      <label class="fw-bold text-muted small">STATUS</label>
                      <div class="mt-1">
                        <span class="badge bg-success-soft text-success">
                          <i class="fas fa-check-circle me-1"></i>
                          Active
                        </span>
                      </div>
                    </div>

                    <div class="info-item mb-3">
                      <label class="fw-bold text-muted small">JOINED DATE</label>
                      <div class="mt-1 text-dark">
                        <i class="fas fa-calendar me-2 text-muted"></i>
                        {{ formatDate(parentToView.created_at) }}
                      </div>
                    </div>

                    <div class="info-item">
                      <label class="fw-bold text-muted small">LAST UPDATED</label>
                      <div class="mt-1 text-dark">
                        <i class="fas fa-clock me-2 text-muted"></i>
                        {{ formatDate(parentToView.updated_at) }}
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Students and Statistics -->
              <div class="col-lg-8">
                <div class="p-4">
                  <!-- Statistics Cards -->
                  <div class="row mb-4">
                    <div class="col-md-4 mb-3">
                      <div class="card border-0 bg-gradient-info text-white h-100">
                        <div class="card-body text-center">
                          <i class="fas fa-child fa-2x mb-2 opacity-75"></i>
                          <h4 class="mb-1">{{ parentToView.students_count || 0 }}</h4>
                          <small>Students</small>
                        </div>
                      </div>
                    </div>
                    <div class="col-md-4 mb-3">
                      <div class="card border-0 bg-gradient-warning text-white h-100">
                        <div class="card-body text-center">
                          <i class="fas fa-graduation-cap fa-2x mb-2 opacity-75"></i>
                          <h4 class="mb-1">{{ parentToView.active_students_count || 0 }}</h4>
                          <small>Active Students</small>
                        </div>
                      </div>
                    </div>
                    <div class="col-md-4 mb-3">
                      <div class="card border-0 bg-gradient-success text-white h-100">
                        <div class="card-body text-center">
                          <i class="fas fa-school fa-2x mb-2 opacity-75"></i>
                          <h4 class="mb-1">{{ parentToView.sections_count || 0 }}</h4>
                          <small>Sections</small>
                        </div>
                      </div>
                    </div>
                  </div>

                  <!-- Students List -->
                  <div class="row">
                    <div class="col-12 mb-4">
                      <div class="card border-0 shadow-sm h-100">
                        <div class="card-header bg-white border-bottom">
                          <h6 class="m-0 fw-bold text-primary">
                            <i class="fas fa-child me-2"></i>
                            Students
                          </h6>
                        </div>
                        <div class="card-body">
                          <div v-if="parentToView.students_count > 0">
                            <div class="student-item mb-3" v-for="i in Math.min(parentToView.students_count, 5)" :key="i">
                              <div class="d-flex align-items-center">
                                <div class="student-icon bg-info-soft text-info rounded-circle me-3">
                                  <i class="fas fa-user-graduate"></i>
                                </div>
                                <div>
                                  <div class="fw-bold">Student {{ i }}</div>
                                  <small class="text-muted">Grade Level</small>
                                </div>
                              </div>
                            </div>
                            <div v-if="parentToView.students_count > 5" class="text-center">
                              <small class="text-muted">+{{ parentToView.students_count - 5 }} more students</small>
                            </div>
                          </div>
                          <div v-else class="text-center py-4">
                            <i class="fas fa-child fa-2x text-muted mb-2"></i>
                            <p class="text-muted mb-0">No students assigned</p>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="modal-footer bg-light">
            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
              <i class="fas fa-times me-2"></i>
              Close
            </button>
            <button type="button" class="btn btn-warning" @click="editParent(parentToView)" data-bs-dismiss="modal">
              <i class="fas fa-edit me-2"></i>
              Edit Parent
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Delete Confirmation Modal -->
    <div class="modal fade" id="deleteModal" tabindex="-1" aria-labelledby="deleteModalLabel" aria-hidden="true">
      <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content border-0 shadow-sm">
          <div class="modal-header bg-danger text-white border-0">
            <h5 class="modal-title fw-bold" id="deleteModalLabel">
              <i class="fas fa-exclamation-triangle me-1"></i> Confirm Delete
            </h5>
            <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
          </div>
          <div class="modal-body p-4">
            <div class="mb-3">
              <h6 class="fw-semibold text-center">Are you sure you want to delete this parent/guardian?</h6>
              <div class="d-flex align-items-center justify-content-center mt-3">
                <div class="parent-avatar bg-primary text-white rounded-circle d-flex align-items-center justify-content-center me-2" style="width: 36px; height: 36px;">
                  <i class="fas fa-user"></i>
                </div>
                <div>
                  <div class="fw-bold text-dark">{{ parentToDelete?.name }}</div>
                  <small class="text-muted">{{ parentToDelete?.email }}</small>
                </div>
              </div>
            </div>
            <div class="alert alert-warning alert-dismissible fade show small border-0" role="alert">
              <i class="fas fa-exclamation-triangle me-2"></i>
              <strong>Warning:</strong> This action is permanent and cannot be undone.
              <button type="button" class="btn-close btn-sm" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
          </div>
          <div class="modal-footer border-top pt-3">
            <button type="button" class="btn btn-outline-secondary btn-sm px-3" data-bs-dismiss="modal">
              Cancel
            </button>
            <button
              type="button"
              class="btn btn-danger btn-sm px-4"
              @click="deleteParent"
              :disabled="deleteForm.processing"
              aria-label="Delete parent"
            >
              <span v-if="deleteForm.processing" class="d-flex align-items-center">
                <LoadingSpinner :show="true" :size="16" :border="2" class="me-2" />
                Deleting...
              </span>
              <span v-else>Delete Parent</span>
            </button>
          </div>
        </div>
      </div>
    </div>
  </AdminLayout>
</template>

<script>
import AdminLayout from '@/Layouts/AdminLayout.vue'
import LoadingSpinner from '@/Components/LoadingSpinner.vue'
import { Link, router, useForm } from '@inertiajs/vue3'
import {
  showSuccessToast,
  showErrorToast
} from '@/Components/SweetAlert.js'

export default {
  name: 'ParentsIndex',
  components: {
    AdminLayout,
    LoadingSpinner,
    Link
  },
  props: {
    parents: Object
  },
  data() {
    return {
      parentToDelete: null,
      parentToView: null,
      searchQuery: '',
      filterStatus: 'all',
      filteredParents: [],
      deleteForm: useForm({})
    }
  },
  computed: {
    parentsWithStudents() {
      return this.parents.data.filter(parent => parent.students_count > 0).length
    },
    newThisMonth() {
      const currentMonth = new Date().getMonth()
      const currentYear = new Date().getFullYear()
      return this.parents.data.filter(parent => {
        const createdDate = new Date(parent.created_at)
        return createdDate.getMonth() === currentMonth && createdDate.getFullYear() === currentYear
      }).length
    }
  },
  mounted() {
    this.filteredParents = [...this.parents.data]

    // Ensure Bootstrap is available
    if (typeof window.bootstrap === 'undefined') {
      console.warn('Bootstrap is not loaded')
    }
  },
  methods: {
    viewParent(parent) {
      this.parentToView = parent

      this.$nextTick(() => {
        const modalElement = document.getElementById('viewParentModal')
        if (modalElement) {
          try {
            if (window.bootstrap && window.bootstrap.Modal) {
              const modal = new window.bootstrap.Modal(modalElement)
              modal.show()
            } else {
              // Fallback: manually show modal
              modalElement.classList.add('show')
              modalElement.style.display = 'block'
              modalElement.setAttribute('aria-modal', 'true')
              modalElement.setAttribute('role', 'dialog')

              // Add backdrop
              const backdrop = document.createElement('div')
              backdrop.className = 'modal-backdrop fade show'
              backdrop.id = 'view-modal-backdrop'
              document.body.appendChild(backdrop)
              document.body.classList.add('modal-open')
            }
          } catch (error) {
            console.error('Error opening view modal:', error)
          }
        }
      })
    },
    editParent(parent) {
      router.visit(`/admin/parents/${parent.id}/edit`)
    },
    confirmDelete(parent) {
      this.parentToDelete = parent

      this.$nextTick(() => {
        const modalElement = document.getElementById('deleteModal')
        if (modalElement) {
          try {
            if (window.bootstrap && window.bootstrap.Modal) {
              const modal = new window.bootstrap.Modal(modalElement)
              modal.show()
            } else {
              // Fallback: manually show modal
              modalElement.classList.add('show')
              modalElement.style.display = 'block'
              modalElement.setAttribute('aria-modal', 'true')
              modalElement.setAttribute('role', 'dialog')

              // Add backdrop
              const backdrop = document.createElement('div')
              backdrop.className = 'modal-backdrop fade show'
              backdrop.id = 'delete-modal-backdrop'
              document.body.appendChild(backdrop)
              document.body.classList.add('modal-open')
            }
          } catch (error) {
            console.error('Error opening delete modal:', error)
          }
        }
      })
    },
    deleteParent() {
      if (this.parentToDelete) {
        const parentName = this.parentToDelete.name
        this.deleteForm.delete(`/admin/parents/${this.parentToDelete.id}`, {
          onSuccess: (page) => {
            // Close the modal
            this.closeDeleteModal()

            // Update the parents data immediately
            this.parents = page.props.parents
            this.updateFilteredParents()

            // Show success toast
            showSuccessToast(
              'Parent Deleted!',
              `${parentName} has been removed successfully.`
            )

            // Reset parent to delete
            this.parentToDelete = null
          },
          onError: (errors) => {
            // Close the modal
            this.closeDeleteModal()

            // Show error toast
            const firstError = Object.values(errors)[0]
            showErrorToast(
              'Delete Failed',
              firstError || 'Unable to delete parent. Please try again.'
            )

            // Reset parent to delete
            this.parentToDelete = null
          }
        })
      }
    },
    closeDeleteModal() {
      const modalElement = document.getElementById('deleteModal')
      if (modalElement) {
        try {
          if (window.bootstrap && window.bootstrap.Modal) {
            const modal = window.bootstrap.Modal.getInstance(modalElement)
            if (modal) {
              modal.hide()
            }
          } else {
            // Manual close
            modalElement.classList.remove('show')
            modalElement.style.display = 'none'
            modalElement.removeAttribute('aria-modal')
            modalElement.removeAttribute('role')

            // Remove backdrop
            const backdrop = document.getElementById('delete-modal-backdrop')
            if (backdrop) {
              backdrop.remove()
            }
            document.body.classList.remove('modal-open')
          }
        } catch (error) {
          console.error('Error closing delete modal:', error)
        }
      }
    },
    updateFilteredParents() {
      // Update the filtered parents list and trigger reactivity
      this.filterParents()
    },
    formatDate(dateString) {
      const date = new Date(dateString)
      return date.toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
      })
    },
    filterParents() {
      let filtered = [...this.parents.data]

      // Apply search filter
      if (this.searchQuery) {
        const query = this.searchQuery.toLowerCase()
        filtered = filtered.filter(parent =>
          parent.name.toLowerCase().includes(query) ||
          parent.email.toLowerCase().includes(query)
        )
      }

      // Apply status filter
      if (this.filterStatus === 'with_students') {
        filtered = filtered.filter(parent => parent.students_count > 0)
      } else if (this.filterStatus === 'without_students') {
        filtered = filtered.filter(parent => parent.students_count === 0)
      }

      this.filteredParents = filtered
    },
    setFilter(status) {
      this.filterStatus = status
      this.filterParents()
    }
  },
  watch: {
    searchQuery() {
      this.filterParents()
    }
  }
}
</script>

<style scoped>
/* Statistics Cards */
.bg-gradient-primary {
  background: linear-gradient(135deg, #2c5aa0 0%, #1e3d6f 100%);
}

.bg-gradient-success {
  background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
}

.bg-gradient-info {
  background: linear-gradient(135deg, #17a2b8 0%, #6f42c1 100%);
}

.bg-gradient-warning {
  background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%);
}

/* Parent Avatar */
.parent-avatar {
  width: 45px;
  height: 45px;
  font-size: 1rem;
  background: linear-gradient(135deg, #2c5aa0 0%, #1e3d6f 100%);
}

/* Table Styling */
.parents-table {
  font-size: 0.95rem;
}

.parents-table th {
  font-weight: 600;
  color: #2c5aa0;
  background-color: #f8f9fa;
  border-bottom: 2px solid #e9ecef;
  padding: 1rem 0.75rem;
}

.parent-row {
  transition: none; /* Remove hover transition */
}

.parent-row:hover {
  background-color: #f8f9fa;
  transform: none; /* Remove transform animation */
  box-shadow: none; /* Remove shadow animation */
}

/* Badge Styling */
.bg-info-soft {
  background-color: rgba(23, 162, 184, 0.1);
  border: 1px solid rgba(23, 162, 184, 0.2);
}

.bg-success-soft {
  background-color: rgba(40, 167, 69, 0.1);
  border: 1px solid rgba(40, 167, 69, 0.2);
}

.bg-primary-soft {
  background-color: rgba(13, 110, 253, 0.1);
}

.bg-warning-soft {
  background-color: rgba(255, 193, 7, 0.1);
  color: #856404;
}

.text-info {
  color: #17a2b8 !important;
}

.text-success {
  color: #28a745 !important;
}

/* Button Group */
.btn-group .btn {
  border-radius: 0.375rem;
  margin-right: 0.25rem;
  transition: none; /* Remove button transitions */
}

.btn-group .btn:last-child {
  margin-right: 0;
}

.btn-group .btn:hover {
  transform: none; /* Remove button hover transform */
  box-shadow: none; /* Remove button hover shadow */
}

/* Empty State */
.empty-state {
  padding: 3rem 2rem;
}

/* Search and Filter */
.input-group-text {
  background-color: #f8f9fa;
  border-color: #e9ecef;
}

.form-control:focus {
  border-color: #2c5aa0;
  box-shadow: 0 0 0 0.2rem rgba(44, 90, 160, 0.25);
}

/* Modal Styling */
.modal-content {
  border: none;
  border-radius: 0.75rem;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
}

.modal-header {
  border-radius: 0.75rem 0.75rem 0 0;
  border-bottom: none;
  padding: 1.5rem;
}

.modal-body {
  padding: 1.5rem;
}

.modal-footer {
  border-top: 1px solid #e9ecef;
  border-radius: 0 0 0.75rem 0.75rem;
  padding: 1rem 1.5rem;
}

/* Parent View Modal Styles */
.parent-avatar-large {
  width: 80px;
  height: 80px;
  font-size: 1.5rem;
}

.info-section .info-item label {
  font-size: 0.7rem;
  font-weight: 600;
  letter-spacing: 0.5px;
  color: #6c757d;
}

.student-icon {
  width: 35px;
  height: 35px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.875rem;
}

/* Alert Styling */
.alert {
  border-radius: 0.5rem;
  border: none;
}

/* Responsive */
@media (max-width: 768px) {
  .parent-avatar {
    width: 40px;
    height: 40px;
    font-size: 0.875rem;
  }

  .btn-group {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
  }

  .btn-group .btn {
    margin-right: 0;
    font-size: 0.875rem;
  }

  .parents-table th,
  .parents-table td {
    padding: 0.75rem 0.5rem;
    font-size: 0.875rem;
  }
}
</style>
